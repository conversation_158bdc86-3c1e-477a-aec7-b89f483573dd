/** @type {import('next').NextConfig} */
const nextConfig = {
  i18n: {
    locales: ["en"],
    defaultLocale: "en", // default lang fr
  },
  reactStrictMode: true,
  images: {
    domains: ['strapiv2.isb.edu','isbstrapi2.quantana.top','isbstrapi.quantana.top','strapionline.isb.edu','prodstrapi.isb.edu','localhost','s3.ap-south-2.amazonaws.com'],},
  async redirects() {
    return [
       
      {
        source: '/leadership-', 
        destination: '/leadership-essentials', 
        permanent: true, 
      },
      {
        source: '/fintech-innovations-from-', 
        destination: '/fintech-innovations-from-blockchain-to-ai', 
        permanent: true, 
      },
      {
        source: '/management-', 
        destination: '/management-essentials', 
        permanent: true, 
      },
      {
        source: '/marketing/:path*', 
        destination: '/', 
        permanent: true, 
      },
      {
        source: '/search/:path*', 
        destination: '/', 
        permanent: true, 
      },
      {
        source: '/business-technology-and-innovation-with-', 
        destination: '/business-technology-and-innovation-with-ai', 
        permanent: true, 
      },
      {
        source: '/fintech-innovations-from-blockchain-to-', 
        destination: '/fintech-innovations-from-blockchain-to-ai', 
        permanent: true, 
      },
      {
          source: '/:learning_track_id/:course',
          has: [{ type: 'query', key: 'redirect', value: 'true' }],
          destination: '/:learning_track_id',
          permanent: true,
       },
      {
          source: '/[learning_track_id]',
          destination: '/',
          permanent: true,
       },
      {
          source: '/v2/:learning_track_id',
          destination: '/',
          permanent: true,
       },
      {
          source: '/lp/[learning_track_id]',
          destination: '/',
          permanent: true,
       },
      {
          source: '/:learning_track_id/StorefrontThankYou',
          destination: '/:learning_track_id',
          permanent: true,
       },
      {
          source: '/freeLessonpage/:path*',
          destination: '/',
          permanent: true,
       },
       {
        source: '/course/:path*',
        destination: '/',
        permanent: true,
      },      
      {
          source: '/uploads/:path*',
          destination: '/',
          permanent: true,
       },
      {
          source: '/thankyou',
          destination: '/landing-page',
          permanent: true,
       },
       {
        source: '/cs-tq',
        destination: '/coming-soon',
        permanent: true,
     },
      {
        source: '/learning-tracks',
        destination: '/',
        permanent: true,
      },
     {
      source: '/lp/:learning_track_id/thankyou',
      destination: '/lp/:learning_track_id',
      permanent: true,
   }
  ]
},  
async headers() {
  return [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Permissions-Policy',
          value: `geolocation=(self ${process.env.NEXT_PUBLIC_BASE_URL})`
        }
      ]
    }
  ]
}

}

module.exports = nextConfig