/** @type {import('next').NextConfig} */
const nextConfig = {
  i18n: {
    locales: ["en"],
    defaultLocale: "en", // default lang fr
  },
  reactStrictMode: true,
  images: {
    domains: ['strapiv2.isb.edu','isbstrapi2.quantana.top','isbstrapi.quantana.top','strapionline.isb.edu','prodstrapi.isb.edu','localhost','s3.ap-south-2.amazonaws.com'],},
  async redirects() {
    return [
      // IMPORTANT: Specific learning track redirects must come FIRST
      // to override dynamic route matching [learning_track_id]/[course]

      // Management Essentials specific redirects
      {
        source: '/management-essentials/project-management',
        destination: '/management-essentials',
        permanent: true,
      },
      {
        source: '/management-essentials/project-management/:path*',
        destination: '/management-essentials',
        permanent: true,
      },
      {
        source: '/management-essentials/:path+',
        destination: '/management-essentials',
        permanent: true,
      },

      // Leadership Essentials specific redirects
      {
        source: '/leadership-essentials/leading-teams',
        destination: '/leadership-essentials',
        permanent: true,
      },
      {
        source: '/leadership-essentials/leading-teams/:path*',
        destination: '/leadership-essentials',
        permanent: true,
      },
      {
        source: '/leadership-essentials/:path+',
        destination: '/leadership-essentials',
        permanent: true,
      },

      // Business Strategy specific redirects
      {
        source: '/business-strategy/business-model-innovation',
        destination: '/business-strategy',
        permanent: true,
      },
      {
        source: '/business-strategy/economics-for-managers',
        destination: '/business-strategy',
        permanent: true,
      },
      {
        source: '/business-strategy/strategy-formulation',
        destination: '/business-strategy',
        permanent: true,
      },
      {
        source: '/business-strategy/strategic-thinking',
        destination: '/business-strategy',
        permanent: true,
      },
      {
        source: '/business-strategy/:path+',
        destination: '/business-strategy',
        permanent: true,
      },

      // Mastering Executive Presence specific redirects
      {
        source: '/mastering-executive-presence/executive-communication',
        destination: '/mastering-executive-presence',
        permanent: true,
      },
      {
        source: '/mastering-executive-presence/:path+',
        destination: '/mastering-executive-presence',
        permanent: true,
      },

      // Business Technology and Innovation with AI specific redirects
      {
        source: '/business-technology-and-innovation-with-ai/digital-transformation',
        destination: '/business-technology-and-innovation-with-ai',
        permanent: true,
      },
      {
        source: '/business-technology-and-innovation-with-ai/:path+',
        destination: '/business-technology-and-innovation-with-ai',
        permanent: true,
      },

      // Fintech Innovations specific redirects
      {
        source: '/fintech-innovations-from-blockchain-to-ai/blockchain-fundamentals',
        destination: '/fintech-innovations-from-blockchain-to-ai',
        permanent: true,
      },
      {
        source: '/fintech-innovations-from-blockchain-to-ai/:path+',
        destination: '/fintech-innovations-from-blockchain-to-ai',
        permanent: true,
      },

      // AI in Business specific redirects
      {
        source: '/ai-in-business/machine-learning',
        destination: '/ai-in-business',
        permanent: true,
      },
      {
        source: '/ai-in-business/:path+',
        destination: '/ai-in-business',
        permanent: true,
      },

      {
        source: '/leadership-',
        destination: '/leadership-essentials',
        permanent: true,
      },
      {
        source: '/fintech-innovations-from-', 
        destination: '/fintech-innovations-from-blockchain-to-ai', 
        permanent: true, 
      },
      {
        source: '/management-', 
        destination: '/management-essentials', 
        permanent: true, 
      },
      {
        source: '/marketing/:path*', 
        destination: '/', 
        permanent: true, 
      },
      {
        source: '/search/:path*', 
        destination: '/', 
        permanent: true, 
      },
      {
        source: '/business-technology-and-innovation-with-', 
        destination: '/business-technology-and-innovation-with-ai', 
        permanent: true, 
      },
      {
        source: '/fintech-innovations-from-blockchain-to-', 
        destination: '/fintech-innovations-from-blockchain-to-ai', 
        permanent: true, 
      },
      {
          source: '/:learning_track_id/:course',
          has: [{ type: 'query', key: 'redirect', value: 'true' }],
          destination: '/:learning_track_id',
          permanent: true,
       },
      {
          source: '/[learning_track_id]',
          destination: '/',
          permanent: true,
       },
      {
          source: '/v2/:learning_track_id',
          destination: '/',
          permanent: true,
       },
      {
          source: '/lp/[learning_track_id]',
          destination: '/',
          permanent: true,
       },
      {
          source: '/:learning_track_id/StorefrontThankYou',
          destination: '/:learning_track_id',
          permanent: true,
       },
      {
          source: '/freeLessonpage/:path*',
          destination: '/',
          permanent: true,
       },
       {
        source: '/course/:path*',
        destination: '/',
        permanent: true,
      },      
      {
          source: '/uploads/:path*',
          destination: '/',
          permanent: true,
       },
      {
          source: '/thankyou',
          destination: '/landing-page',
          permanent: true,
       },
       {
        source: '/cs-tq',
        destination: '/coming-soon',
        permanent: true,
     },
      {
        source: '/learning-tracks',
        destination: '/',
        permanent: true,
      },
     {
      source: '/lp/:learning_track_id/thankyou',
      destination: '/lp/:learning_track_id',
      permanent: true,
   },
   // Business Strategy redirects
   {
     source: '/business-strategy/business-model-innovation',
     destination: '/business-strategy',
     permanent: true,
   },
   {
     source: '/business-strategy/economics-for-managers',
     destination: '/business-strategy',
     permanent: true,
   },
   {
     source: '/business-strategy/strategy-formulation',
     destination: '/business-strategy',
     permanent: true,
   },
   // Handle business strategy URLs with query parameters
   {
     source: '/business-strategy/economics-for-managers/:path*',
     destination: '/business-strategy',
     permanent: true,
   },
   {
     source: '/business-strategy/strategy-formulation/:path*',
     destination: '/business-strategy',
     permanent: true,
   },
   // General business strategy catch-all for any other invalid sub-paths
   {
     source: '/business-strategy/:path+',
     destination: '/business-strategy',
     permanent: true,
   },
   // Management Essentials redirects
   {
     source: '/management-essentials/:path+',
     destination: '/management-essentials',
     permanent: true,
   },
   // Leadership Essentials redirects
   {
     source: '/leadership-essentials/:path+',
     destination: '/leadership-essentials',
     permanent: true,
   },
   // Mastering Executive Presence redirects
   {
     source: '/mastering-executive-presence/:path+',
     destination: '/mastering-executive-presence',
     permanent: true,
   },
   // Business Technology and Innovation with AI redirects
   {
     source: '/business-technology-and-innovation-with-ai/:path+',
     destination: '/business-technology-and-innovation-with-ai',
     permanent: true,
   },
   // Fintech Innovations from Blockchain to AI redirects
   {
     source: '/fintech-innovations-from-blockchain-to-ai/:path+',
     destination: '/fintech-innovations-from-blockchain-to-ai',
     permanent: true,
   },
   // AI in Business redirects
   {
     source: '/ai-in-business/:path+',
     destination: '/ai-in-business',
     permanent: true,
   },
   // Additional specific redirects for common invalid patterns
   // Management Essentials specific patterns
   {
     source: '/management-essentials/StorefrontThankYou',
     destination: '/management-essentials',
     permanent: true,
   },
   // Leadership Essentials specific patterns
   {
     source: '/leadership-essentials/StorefrontThankYou',
     destination: '/leadership-essentials',
     permanent: true,
   },
   // Business Strategy specific patterns
   {
     source: '/business-strategy/StorefrontThankYou',
     destination: '/business-strategy',
     permanent: true,
   },
   // Mastering Executive Presence specific patterns
   {
     source: '/mastering-executive-presence/StorefrontThankYou',
     destination: '/mastering-executive-presence',
     permanent: true,
   },
   // Business Technology and Innovation with AI specific patterns
   {
     source: '/business-technology-and-innovation-with-ai/StorefrontThankYou',
     destination: '/business-technology-and-innovation-with-ai',
     permanent: true,
   },
   // Fintech Innovations specific patterns
   {
     source: '/fintech-innovations-from-blockchain-to-ai/StorefrontThankYou',
     destination: '/fintech-innovations-from-blockchain-to-ai',
     permanent: true,
   },
   // AI in Business specific patterns
   {
     source: '/ai-in-business/StorefrontThankYou',
     destination: '/ai-in-business',
     permanent: true,
   },
   // Common course/module patterns for all learning tracks
   // Management Essentials course patterns
   {
     source: '/management-essentials/project-management',
     destination: '/management-essentials',
     permanent: true,
   },
   {
     source: '/management-essentials/project-management/:path*',
     destination: '/management-essentials',
     permanent: true,
   },
   // Leadership Essentials course patterns
   {
     source: '/leadership-essentials/leading-teams',
     destination: '/leadership-essentials',
     permanent: true,
   },
   {
     source: '/leadership-essentials/leading-teams/:path*',
     destination: '/leadership-essentials',
     permanent: true,
   },
   // Business Strategy course patterns
   {
     source: '/business-strategy/strategic-thinking',
     destination: '/business-strategy',
     permanent: true,
   },
   {
     source: '/business-strategy/strategic-thinking/:path*',
     destination: '/business-strategy',
     permanent: true,
   },
   // Mastering Executive Presence course patterns
   {
     source: '/mastering-executive-presence/executive-communication',
     destination: '/mastering-executive-presence',
     permanent: true,
   },
   {
     source: '/mastering-executive-presence/executive-communication/:path*',
     destination: '/mastering-executive-presence',
     permanent: true,
   },
   // Business Technology and Innovation with AI course patterns
   {
     source: '/business-technology-and-innovation-with-ai/digital-transformation',
     destination: '/business-technology-and-innovation-with-ai',
     permanent: true,
   },
   {
     source: '/business-technology-and-innovation-with-ai/digital-transformation/:path*',
     destination: '/business-technology-and-innovation-with-ai',
     permanent: true,
   },
   // Fintech Innovations course patterns
   {
     source: '/fintech-innovations-from-blockchain-to-ai/blockchain-fundamentals',
     destination: '/fintech-innovations-from-blockchain-to-ai',
     permanent: true,
   },
   {
     source: '/fintech-innovations-from-blockchain-to-ai/blockchain-fundamentals/:path*',
     destination: '/fintech-innovations-from-blockchain-to-ai',
     permanent: true,
   },
   // AI in Business course patterns
   {
     source: '/ai-in-business/machine-learning',
     destination: '/ai-in-business',
     permanent: true,
   },
   {
     source: '/ai-in-business/machine-learning/:path*',
     destination: '/ai-in-business',
     permanent: true,
   }
  ]
},  
async headers() {
  return [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Permissions-Policy',
          value: `geolocation=(self ${process.env.NEXT_PUBLIC_BASE_URL})`
        }
      ]
    }
  ]
}

}

module.exports = nextConfig