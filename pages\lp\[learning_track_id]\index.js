import Link from "next/link";
import Image from "next/image";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import appLogo from "../../../assets/ISB_Online Logo.png";
import classes from "./index.module.css";
import classes1 from "../../finance.module.css"
import React, { useMemo, useState } from "react";
import DataTable from "../../../components/DataTable";
import Group from "../../../assets/New Online Documents.svg";
import projectduration from "../../../assets/New project duration.svg";
import RupessDocuments from "../../../assets/New Rupees Documents.svg";
import verfiedReport from "../../../assets/New Verified Report.svg";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useRef, useEffect } from "react";
import useCollapse from "react-collapsed";
import { useRouter } from "next/router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "react-phone-input-2/lib/style.css";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { NextSeo } from "next-seo";
import group from "../../../assets/Group_2688.png";
import playIcon from "../../../assets/play_icon.png";
import dynamic from "next/dynamic";
import { KeyConcepts } from "../../../components/KeyConcepts";
import { FacultySlider } from "../../../components/FacultySlider";
import { LeadForm } from "../../../components/LeadForm";
import { Whowillbenefit } from "../../../components/Whowillbenefit";
import { GetLocation } from "../../../components/GeoLocation";
import { StoriesSlider } from "../../../components/StoriesSlider";
import HighlightsSection from "../../../components/HighlightsSection";
import LandingPageHeroText from "../../../components/LandingPageHeroText";
import LpEligibilityBanner from "../../../components/LpEligibilityBanner";
import TrackPageBottomFold from "../../../components/TrackPageBottomFold";
import { WhatYouGain } from "../../../components/WhatYouGain";
import { ThankYouSection } from "../../../components/ThankYouSection";
import { IsbAdvantage } from "../../../components/IsbAdvantage";
import banner from "../../../assets/footer_banner.jpeg";
import CardContainer from "../../../components/CardContainer";
import Card from "../../../components/Card";
import { AiComponent } from "../../../components/AiComponent";
import { CustomModal, CustomModalBody } from "../../../components/ProgramDetailsCard";
import { BrochureFileNameFormatter, MIN_NAME_LENGTH } from "../../../utils";
import OnlineAdvantage from "../../../components/OnlineAdvantage";


const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });


export default function LandingPageV2(props) {

  const router = useRouter();

  const landingPageResponse = props.apiData.data[0].attributes;
  const bannerFullData = props.stickyBanner.data[0].attributes
  const [countryCodeEntry,setCountryCodeEntry] = useState()
  const [coutrycode_exclude,setCoutrycode_exclude] = useState()
  const [thankyouScreen,setThankyouScreen] = useState(false)
  const [completePath, setCompletePath] = useState('');
  const [queryParams, setQueryParams] = useState('');
  
  const learnTrackData= landingPageResponse.new_track_page.data.attributes
  const [currentLocation, setCurrentLocation] = useState({ city:'', state: '', country: ''});
  const trackFoldData = props.trackFold.data.attributes;
  const isV2 = props.isV2;

  useEffect(() => {
    const fetchLocation = async () => {
      try {
        const loc = await GetLocation();
        setCurrentLocation(loc);
    } catch (error) {
      console.error(error);
    }
  };
  fetchLocation();
}, [ ])

 
 

 

  const { query } = useRouter();
  const [agree, setAgree] = useState(true);

  const [showIframe, setShowIframe] = useState(false);

  const handleImageClick1 = () => {
    if(landingPageResponse?.Iframe_fold?.Iframe_url){
      setAutoplayMobile(true);
      setShowIframe(true);
    }else{
      return null;
    }
  };


  const [isImageHidden, setIsImageHidden] = useState(false);
  const [autoplay, setAutoplay] = useState(false);
  const [autoplayMobile, setAutoplayMobile] = useState(false);


  const handleImageClick = () => {
    if(landingPageResponse?.Iframe_fold?.Iframe_url){
      setIsImageHidden(true);
      setAutoplay(true);
    }else{
      return null;
    }
  };


  const checkboxHandler = () => {
    setAgree(true);
    return false;
  };
 
  const [thankYouModal, setThankYouModal] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    number: '',
    role: 'Select',
    agree: true,
    years_of_experience:'',
    location: ""
  });
  useEffect(() => {
    if ( currentLocation.state || currentLocation.country || currentLocation.city ) {
      setFormData((prevData) => ({
        ...prevData,
        location: `${currentLocation.city}, ${currentLocation.state}, ${currentLocation.country}`
      }));
    }
  }, [currentLocation]);
 
  const tags =`utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
  const url = `${props.baseurl}${isV2 ? `/` : `/v2/`}${props.meetupId}&${tags}`
  
  const [formErrors, setFormErrors] = useState({});

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showFinancOptionsModal, setShowFinancOptionsModal] = useState(false);
  const [showGroupEnrolment, setShowGroupEnrolment] = useState(false);
  
  // Financing data state
  const [financingData, setFinancingData] = useState({ 
    header: [], 
    data: [], 
    description: "", 
    footer_text: "" ,
    finance_options_title:""
  });
  const [financingLoading, setFinancingLoading] = useState(true);
  const [financingError, setFinancingError] = useState(null);
  
  // Fetch financing data
  useEffect(() => {
    const fetchFinancingData = async () => {
      try {
        setFinancingLoading(true);
        const apiUrl = props.apiUrl;
        
        const response = await fetch(`${apiUrl}/api/financing-table`);
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Handle both the new format (with header/data) and legacy format
        if (data.data.attributes.table) {
          // Extract header, data, description and footer_text from API response
          const { header, data: tableData } = data.data.attributes.table;
          const { description, footer_text,finance_options_title } = data.data.attributes;
          
          setFinancingData({
            header: header || [],
            data: tableData || [],
            description: description || '',
            footer_text: footer_text || '',
            finance_options_title:finance_options_title || ''
          });
        } else {
          // Fallback for empty data
          setFinancingData({
            header: [], 
            data: [], 
            description: '',
            footer_text: '',
            finance_options_title:''
          });
        }
      } catch (err) {
        console.error('Error fetching financing data:', err);
        setFinancingError(err.message);
      } finally {
        setFinancingLoading(false);
      }
    };

    fetchFinancingData();
  }, [props.apiUrl]);

  const handleClosePaymentModal = (groupleadflag) => {
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="unset"
    document.documentElement.style.paddingRight="unset"
    setShowPaymentModal(false)
    setShowFinancOptionsModal(false)
    setThankYouModal(false)
    setShowGroupEnrolment(false)

    setShowGroupEnrolment(false)
    if(groupleadflag===true){
      document.getElementById("modal_content").scrollTop = 0;
      document.documentElement.style.overflow="hidden"
      document.documentElement.style.paddingRight="17px"
      setThankYouModal(true)
    }else{
      setThankYouModal(false)

    }

    setFormErrors({})
  };
  const handleShowPaymentModal = (e) => {
    const modalElement = document.getElementById("modal_content");
    if (modalElement) {
        modalElement.scrollTop = 0;
    } else {
        console.warn("Modal content element not found");
    }
    document.documentElement.style.overflow="hidden"
    document.documentElement.style.paddingRight="17px"
    const id= e.target.id
    if(id==="payment_modal"){
      setShowPaymentModal(true);
    }else if(id==="financing_options"){
      setShowFinancOptionsModal(true)
    }else if(id==="group_enrolment"){
      setShowGroupEnrolment(true)
      // setThankYouModal(true)
    }
   
  }

 
  const handleChange = (e) => {
    const { name, value } = e.target;
    let updatedValue = value;
  
    // Apply text-only validation for the "location" field
    if (name === "location") {
      updatedValue = value.replace(/[^a-zA-Z\s]/g, '');
    }
  
    setFormData((prevData) => ({
      ...prevData,
      [name]: updatedValue,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };


const handleChange1=(e)=>{
 
if(Number(e.slice(0,2)) ===91){
  setCountryCodeEntry(e.slice(0,2))
  let sliced_mobile = e.slice(2,12)
      setCoutrycode_exclude(sliced_mobile)

}else if(Number(e.slice(0,3)) ===971){
  setCountryCodeEntry(e.slice(0,3))
  let sliced_mobile = e.slice(3,13)
  setCoutrycode_exclude(sliced_mobile)

}else if(Number(e.slice(0,2)) ===65 || Number(e.slice(0,2)) ===61){
  setCountryCodeEntry(e.slice(0,2))
  let sliced_mobile = e.slice(2,13)
  setCoutrycode_exclude(sliced_mobile)
  
}

else{
  setCountryCodeEntry(e)
}

 
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        "number": '',
      }));
    
    setFormData((prevData) => ({
      ...prevData,
      "number": e,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      "number": '',
    }));
}

  const isValidEmail = (email) => {
    return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email);
  };
 
  const isValidMobileNumber = (number) => {
    let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    return /^[0-9]{0,15}$/.test(cleanedNumber);
  };
  
  useEffect(() => {
    if (window.location.hash === '#thankyou') {
      router.replace(window.location.pathname, undefined, { shallow: true });
    }
  }, []);

  
   
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname.split('/').slice(1).join('/');  
      const qParams = window.location.search.slice(1); 
      const combinedUrl = `${props.baseurl}/${currentPath}&${qParams}`;
      setCompletePath(combinedUrl);
      const params = window.location.search.slice(1); 
      setQueryParams(`${params}`);
    }
  }, []);

  const [buttonDisabled, setButtonDisabled] = useState(false);
  const submitContact = async (event) => {
    event.preventDefault();
    const errors = {};
    setButtonDisabled(true);

    if (!formData.name) {
      errors.name = 'Name is required';
    }
    if( formData.name.length<MIN_NAME_LENGTH){
      errors.name = 'Name should be atleast 3 characters';
    }
    if (!formData.years_of_experience) {
      errors.years_of_experience = 'Years of experience is required';
    }
    if (!formData.location && !currentLocation.state) {
      errors.location = 'Location is required';
    }

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.number) {
      errors.number = 'Mobile number is required';
    } 
   if(countryCodeEntry){
     if (countryCodeEntry.slice(0,2) ==91 && (coutrycode_exclude).length<10) {
      errors.number = 'Please enter valid mobile number'; 
    }else if((countryCodeEntry.slice(0,2) ==65 || countryCodeEntry.slice(0,2) ==61) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter valid mobile number'; 
    }
    else if((countryCodeEntry.slice(0,3) ==971) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter a valid mobile number '; 
    }
  }
    else if (!isValidMobileNumber(event.target.number.value)) {
      errors.number = 'Please enter a valid mobile number';
    }

    if (formData.role === 'Select') {
      errors.role = 'Please select a role';
    }



    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setButtonDisabled(false);
      return;
    }
   
        let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    const getCountryCode = (phoneNumber) =>
      phoneNumber.match(/^\+(\d{1,3})/)?.[1];
    const countryCode = getCountryCode(event.target.number.value);

    // const now = new Date();
    // const expirationDate = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);

    const json = {
      first_name: formData.name,
      email: formData.email.toLowerCase(),
      country_code: countryCode,
      mobile: cleanedNumber,
      years_of_experience:formData.years_of_experience,
      location: formData.location,
      role: formData.role,
      url: completePath,
      program_id:
        process.env.NEXT_PUBLIC_BASE_URL === 'https://online.isb.edu'
          ? landingPageResponse.prod_program_id
          : landingPageResponse.staging_program_id,
      tags: `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`,
    };


    const response = await fetch(
      `${process.env.NEXT_PUBLIC_LANDINGPAGE_SFDC_URL}/backend/free_lesson/user_create`,
      {
        body: JSON.stringify(json),
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
      }
    );

    const qpms =
    query.utm_source !== undefined
      ? `utm_source=${query.utm_source}&utm_medium=${query.utm_medium || ''}&utm_campaign=${query.utm_campaign || ''}&utm_term=${query.utm_term || ''}&utm_content=${query.utm_content || ''}`
      : '';
  
  const andQpms = qpms ? `?${qpms}` : '';


const data = await response.json();
    const userId = data.user_id;
    if (response.status === 200) {
      const now = new Date();
      const expirationDate = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000); // 60days from now
      document.cookie = `leadform_name=${formData.name.trim()}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_email=${formData.email?.trim()}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_mobile=${json.mobile}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_country_code=${json.country_code}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_ProgramId=${json.program_id}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_role=${formData.role}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_id=${userId}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_location=${formData.location}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_years_of_experience=${formData.years_of_experience}; Expires=${expirationDate.toUTCString()}; path=/;`;

      const cookies = document.cookie.split(';')
      handleDownload();
    //  setThankyouScreen(true)
    router.push(
      {
        pathname: `/lp/${props.meetupId}/thankyou`,
        query: {...query, showModal: true }, 
      },
      `${`/lp/${props.meetupId}/thankyou${qpms ? `?${queryParams}` : ''}`}`, { shallow: true }
    );
    }
    else{
      setButtonDisabled(false);
    }
  };


  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    window.addEventListener("scroll", () => {
      if (window.pageYOffset > 300) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    });
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth", // for smoothly scrolling
    });
  };

  const C = (
    <FontAwesomeIcon
      icon={faMinus}
      className="fa-solid fa-minus"
      style={{ color: "#7C8495" }}
    />
  );
  const E = (
    <FontAwesomeIcon
      icon={faPlus}
      className="fa-solid fa-plus"
      style={{ color: "#7C8495" }}
    />
  );


  const Collapse = ({ index, ...data }) => {
    
    const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();

    return (
      <>
        <div>
          <h5
            className={`${classes.faqQuestion} d-flex py-2 justify-content-between text-black m-0 mb-2`}
            {...getToggleProps()}
          >
            {`Course ${index+1}: ${data.attributes.course_title}`}

            {isExpanded ? C : E}
          </h5>
          <div {...getCollapseProps()}>
            <div dangerouslySetInnerHTML={{ __html: data.attributes.landingpage_course_description }}></div>
          </div>
        </div>
        <div className={`mb-3 p-0 ${classes.foottitle}`}></div>
      </>
    );
  };

 

  const handleDownload = async () => {
    try {
      const response = await fetch(
        props.apiUrl + landingPageResponse.brochure.data.attributes.url
      );
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const formattedName = BrochureFileNameFormatter(props.meetupId);
      link.download = `${formattedName +".pdf"}`;
      link.click();
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };
  

  const scrollToOverview = (target) => {
    const scrollTarget = document.getElementById("overview");
    if (scrollTarget) {
      let offset = 50.0; // Default offset for desktop view

      if (window.innerWidth <= 768) {
        offset = 0; // Offset for mobile view
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
      });
    }
  };
  const scrollToHighlights = (target) => {
    const scrollTarget = document.getElementById("Highlights");
    if (scrollTarget) {
      let offset = 0; 

      if (window.innerWidth <= 768) {
        offset = 0; 
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", 
      });
    }
  };
  const scrollToFaculty = (target) => {
    const scrollTarget = document.getElementById("Faculty");
    if (scrollTarget) {
      let offset = 0; // Default offset for desktop view

      if (window.innerWidth <= 768) {
        offset = 0; // Offset for mobile view
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
      });
    }
  };
  const scrollToEligibility = (target) => {
    const scrollTarget = document.getElementById("Eligibility");
    if (scrollTarget) {
      let offset = 0; // Default offset for desktop view

      if (window.innerWidth <= 768) {
        offset = 0; // Offset for mobile view
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", 
      });
    }
  };
 
  const [showSticky, setShowSticky] = useState(false);
  const scrollDivRef = useRef(null);
  const [scrolled, setScrolled] = useState(false);
  useEffect(() => {
    const handleScroll = () => {
      if(window.scrollY>100){
        setScrolled(true)
      }
      if (scrollDivRef.current) {
        const div = scrollDivRef.current;
        const rect = div.getBoundingClientRect();

        if (rect.top < 0) {
          setShowSticky(true);
        } else {
          setShowSticky(false);
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const professors = landingPageResponse.professors.data;

const leadformMinHeight = "auto"

const containerMargin = 'container mt-2 px-0'
 const bannerImagestyle = {
  filter:"brightness(100%)"
 }

 let cards = [];

 if (landingPageResponse.alumni_status_cards) {
   cards = [
     {
       content: landingPageResponse.alumni_status_cards[0]?.description,
       backgroundImage: `${props.apiUrl}${landingPageResponse.alumni_status_cards[0]?.image?.data?.attributes?.formats?.thumbnail?.url || ''}`,
     },
     {
       content: landingPageResponse.alumni_status_cards[1]?.description,
       backgroundImage: `${props.apiUrl}${landingPageResponse.alumni_status_cards[1]?.image?.data?.attributes?.formats?.thumbnail?.url || ''}`,
     },
     {
       content: landingPageResponse.alumni_status_cards[2]?.description,
       backgroundImage: `${props.apiUrl}${landingPageResponse.alumni_status_cards[2]?.image?.data?.attributes?.formats?.thumbnail?.url || ''}`,
     },
   ];
 }
 
 const settings1 = {
  dots: true, // Display carousel indicators
  infinite: true,
  speed: 400,
  autoplay: true,
  autoplaySpeed: 8000,
  slidesToShow: 3, // Display 3 slides on larger screens
  slidesToScroll: 1,
  arrows: false,
  responsive: [
    {
      breakpoint: 768, // Adjust this breakpoint for mobile devices
      settings: {
        slidesToShow: 1, // Display 1 slide on smaller screens
      },
    },
  ],
};

  return (
    <>
      <NextSeo
        // title="Certificate Masters Programme in Management Essentials"
        title={landingPageResponse.meta.title}
        canonical={`${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath}
        noindex = {true}
        nofollow = {true}
        // description="Make informed decisions, inspire your teams, and confidently manage projects by discovering modern management principles' power."
        description={landingPageResponse.meta.description}
        openGraph={{
          type: `website`,
          url: `${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath,
          title: `${landingPageResponse.meta.title}`,

          description: `${landingPageResponse.meta.description}`,
          locale: "en",
          images: [
            {
              url: `${
                props.apiUrl +
                landingPageResponse.meta.image.data.attributes.url
              }`,
              alt: "image.png",
            },
          ],
          site_name: `ISB Online`,
        }}
        twitter={{
          handle: "@handle",
          site: "@site",
          cardType: `${landingPageResponse.meta.title}`,
        }}
        additionalMetaTags={[
          {
            name: "keywords",
            content: `${landingPageResponse.meta.keywords}`,
          },
          {
            name: "robots",
            content: `${landingPageResponse.meta.robots}`,
          },
        ]}
      />

      <Navbar
        // sticky="top"
        className={`${classes.navelems} px-4`}
        bg="white"
        expand="lg"
        id="myNavbar"
      >
        <Navbar.Brand>
          <Image width={170} src={appLogo} alt="ISB Logo" />
        </Navbar.Brand>

        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse
          className="d-base-flex justify-content-end"
          id="basic-navbar-nav"
        >
          <Nav className="text-right" id="myNavItem">
            <Nav.Link as="span">
              <a onClick={scrollToOverview} className={classes.para} href="#">
                Overview
              </a>
            </Nav.Link>

            <Nav.Link as="span">
              <a onClick={scrollToHighlights} className={classes.para} href="#">
                Highlights
              </a>
            </Nav.Link>

            <Nav.Link as="span">
              <a onClick={scrollToFaculty} className={classes.para} href="#">
                Faculty
              </a>
            </Nav.Link>

            <Nav.Link as="span">
              <a
                onClick={scrollToEligibility}
                className={classes.para}
                href="#"
              >
                Eligibility
              </a>
            </Nav.Link>

            <div className='d-none d-lg-block'>
              <DownloadBrocureBtn scrollToTop={scrollToTop} thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>
            </div>

          </Nav>
        </Navbar.Collapse>
      </Navbar>

{/* fold one div */}
      <div
        className={`${classes.fold1bg} ${classes.singupbg} `}
         
      >
        <Image
            src={`${props.apiUrl}${landingPageResponse.hero_image_desktop.data.attributes.url}`}
            alt="DesktopheroImage"
            fill="1000vw"
            priority={true}
            style={bannerImagestyle}
          />
    
        <div className={`row py-lg-5 p-0 ${classes.equalPadding} mx-auto m-0`} style={{ position: "relative", zIndex: 1 }}>
          <div
            className={`${classes.mobileFoldbg} col-md-7 py-lg-0 py-md-4 py-sm-4 py-3 px-0 d-flex flex-column justify-content-center`}
          style={{minHeight:leadformMinHeight}}
          >
         <LandingPageHeroText
         title={landingPageResponse.title}
         classes={classes}
         rating={landingPageResponse.program_rating}
         starText={landingPageResponse.based_on_participant}
         starColor={"orange"}
         />
          </div>
          {!thankyouScreen  ? 
          <>
          <div className="col-1"></div>
            <div id="form" className="col-lg-4 col-md-12 p-4 pt-3 bg-white " style={{  minHeight:leadformMinHeight, maxWidth:"395px"}}>
            
                <div>
                  <p className={`p-0 m-0 mb-2 ${classes.blueBoldText}`}>
                    {landingPageResponse.signup_form.signup_title}
                  </p>
            
                </div>
            
                <LeadForm
                  trackpage={true}
                  myclass={'col-lg-12 pt-0 p-0'}
                  checkboxHandler={checkboxHandler}
                  buttonDisabled={buttonDisabled}
                  handleChange1={handleChange1}
                  handleChange={handleChange}
                  formErrors={formErrors}
                  formData={formData}
                  submitContact={submitContact}
                  landingPageResponse={landingPageResponse}
                />
              </div>
          </>
          
           : <ThankYouSection learnTrackData={learnTrackData} trackFoldData={trackFoldData} landingPageResponse={landingPageResponse} apiUrl={props.apiUrl} handleDownload={handleDownload} meetupId={props.meetupId}/>}
        </div>
      </div>
       

{/* fold one for mobile div */}
      <div
        className={`${classes.equalPadding} position-relative row mx-auto ${classes.showMobileFold}`}
      >
       
        <div className="position-relative text-center px-0">
          <Image
            height={0}
            width={0}
            priority={true}
            sizes="100vw"
            className="col-12 img-fluid p-0"
            alt="heroImage"
            style={bannerImagestyle}
            src={
              props.apiUrl +
              landingPageResponse.hero_image_mobile.data.attributes.url
            }
          ></Image>
            <div
              className={`${classes.mobileimgcontainer} px-0`}
            >
           <LandingPageHeroText
           title={landingPageResponse.title}
           classes={classes}
           rating={landingPageResponse.program_rating}
           starText={landingPageResponse.based_on_participant}
           starColor={"orange"}
           isMobile={true}
           />
            </div>
          
        </div>

{/* mobile lead form div */}
          
        <div className="col-12 bg-white">
          <div className="py-4 ">
            <div>
              <p className={`p-0 m-0 mb-2  text-md-start text-center ${classes.blueBoldText}`}>
                {landingPageResponse.signup_form.signup_title}
              </p>
              {!thankyouScreen  ? <LeadForm
                trackpage={true}
                currentLocation={currentLocation}
                myclass={'col-lg-12 pt-0 p-0'}
                checkboxHandler={checkboxHandler}
                buttonDisabled={buttonDisabled}
                handleChange1={handleChange1}
                handleChange={handleChange}
                formErrors={formErrors}
                formData={formData}
                submitContact={submitContact}
                landingPageResponse={landingPageResponse}
                />: <div className="border px-2 py-3">
                  <ThankYouSection learnTrackData={learnTrackData} trackFoldData={trackFoldData} landingPageResponse={landingPageResponse} apiUrl={props.apiUrl} handleDownload={handleDownload} meetupId={props.meetupId}/>
                </div>}
            </div>
          </div>
        </div>
      </div>
{/* fold two  div */}
      

        <div id="Eligibility" className={`${classes.back_to_top} bg-white`}>
          <LpEligibilityBanner
            landingPageResponse={landingPageResponse}
            classes={classes}
            Group={Group}
            projectduration={projectduration}
            RupessDocuments={RupessDocuments}
            handleShowPaymentModal={handleShowPaymentModal}
            verfiedReport={verfiedReport}
          />
        </div>
    
        <div ref={scrollDivRef}></div>
        {showSticky && (
        <div  className={`${classes.hideButton} ${classes.lp_sticky_bar}`}>
          <div className={`${classes.equalPadding} mx-auto`}>
            <div className="row py-2 py-md-3 py-lg-2 px-lg-0 px-5 px-md-0">
              <div className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder}`}>
                <div className={`row ${classes.small_img}`}>
                  <div className={`col-lg-4 col-3 text-end`}>
                    <Image src={Group} alt="Card" width="0" height="0" />
                  </div>
                  <div className={`col-lg-8 col-9  p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {" "}
                      {landingPageResponse.lt_data.start_title}{" "}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        {landingPageResponse.lt_data.start_date}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                         {landingPageResponse.lt_data.last_date_apply_title}{" "}
                        {landingPageResponse.lt_data.end_date}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder}`}
              >
                <div className={`row`}>
                  <div className="col-lg-4 col-3 text-end">
                    <Image
                      src={projectduration}
                      alt="Card"
                      width="0"
                      height="0"
                      // sizes="100vw"
                    />
                  </div>
                  <div className={`col-lg-8 col-9 p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {landingPageResponse.lt_data.duration_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {landingPageResponse.lt_data.duration_in_weeks} Weeks,
                        Online
                      </p>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {landingPageResponse.lt_data.duration_in_hrs} hours per
                        week
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`col-lg-3 col-12 col-md-6  ${classes.rightBorder}`}
              >
                <div className={`row`}>
                  <div className="col-lg-3 col-3 text-end">
                    <Image
                      src={verfiedReport}
                      alt="Card"
                      width="0"
                      height="0"
                      
                    />
                  </div>
                  <div className={`col-lg-9 col-9 p-0 pe-2`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {landingPageResponse.lt_data.fee_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        INR {landingPageResponse.lt_data.fee}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                      {/* View payment plan */}
                      <FinanceLinks handleShowPaymentModal={handleShowPaymentModal} landingPageResponse={landingPageResponse}/>
                    </p>
                    </div>
                  </div>
                
                </div>
              </div>
              <div className={`col-lg-3 col-12 col-md-6 align-center ${classes.centerAlignText}`}>
              <DownloadBrocureBtn scrollToTop={scrollToTop} thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>

              </div>
            </div>
          </div>
        </div>
      )}

{/*New_fold  div */}

<div id="overview"  className={`${classes.light_bg} ${classes.lpv2videosection} p-0 `}>

<h2 className={classes.blueHeading}>{landingPageResponse.program_guidance_title}</h2>
      <VideoSection 
      autoplayMobile={autoplayMobile}
      bannerFullData={bannerFullData}
      autoplay={autoplay} 
      handleImageClick={handleImageClick} 
      isImageHidden={isImageHidden} 
      apiUrl={props.apiUrl} 
      landingPageResponse={landingPageResponse} 
      handleImageClick1={handleImageClick1} 
      showIframe={showIframe}
      learnTrackData={learnTrackData}
      />
      </div>
       
    <div className="bg-white pb-lg-5 pb-lg-4">
      <Whowillbenefit
        apiUrl={props.apiUrl}
        bannerFullData={bannerFullData}
        learnTrackData={learnTrackData}
        bgColor={"lpv2"}
      />
    </div>

       {landingPageResponse.new_highlightcards?.length>0 && <div id="Highlights" className={`${classes.whitebackground} p-0 m-0`}>
          <HighlightsSection
         classes={classes}
         apiUrl={props.apiUrl}
         data={landingPageResponse.new_highlightcards}
         title={landingPageResponse.new_highlight_title}
         />
          <div className={`col-lg-12 col-md-12 col-12 p-0  mx-auto ${classes.equalPadding}`}
          >
          </div>
        </div>}

        <div>
         {landingPageResponse.AI_Component?.ai_image_component?.length>0 && <AiComponent componentFlag={true} apiUrl={props.apiUrl} landingPageResponse={landingPageResponse}/>}


        </div>

{/* fold Programme Courses  div */}

      <div className="px-2 bg-white">
        <div
          className={`col-lg-12 col-md-12 col-12 mx-auto p-2 ${classes.equalPadding}`}>
          <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
            {landingPageResponse.programme_course_title}
          </h2>

          {landingPageResponse.new_track_page.data.attributes.newcourse_trackpages.data.map(
            (item, i) => {
              return <Collapse key={item.id} index={i} {...item}  />;
            }
          )}

          <p className={`px-2 px-lg-0  ${classes.bdc_note_mobile}`} style={{fontStyle:"italic",fontFamily: "sans-serif"}}>{landingPageResponse.syllabus_note}</p>
        </div>
        <div className={`d-flex justify-content-center bg-white pb-3 ${learnTrackData.story_fold?.length ===0 ? "pb-5":"pb-3"}`}>
        <DownloadBrocureBtn scrollToTop={scrollToTop} thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>
        </div>
      </div>

       
   {learnTrackData.story_fold?.length>0 ?   
   <div className={`px-4`}>
        <StoriesSlider
          apiUrl={props.apiUrl}
          storyData={learnTrackData.story_fold}
          title={bannerFullData.story_fold_title}
          default_settings={true}
          lpflag={true}
        />
      </div> :<div style={{width:"100%", border:"1px solid transparent"}}></div>}

      <div className=" bg-white">
        <div className={`${classes.equalPadding} m-auto`}>
          <div id="Faculty" className={`${classes} bg-white p-0 pb-4`} style={{ position: "relative" }}>
            <FacultySlider
              title={bannerFullData.faculty_fold_title}
              courseProf={professors}
              apiUrl={props.apiUrl}
              flag={true}
            />
          </div>
        </div>
      </div>


      {  <>
        <div className={`${classes.equalPadding} ${containerMargin} px-0`}>
          <WhatYouGain
          bdcnote={landingPageResponse.bdc_note}
            classes={classes}
            title={landingPageResponse.bdc_title}
            certificateData={landingPageResponse.certificate}
            flag={true}
            apiUrl={props.apiUrl}

          />
        </div>

        {/* <HighlightsSection
          classes={classes}
          apiUrl={props.apiUrl}
          data={landingPageResponse.why_isb_online}
          title={landingPageResponse.why_isb_online_title}
          flag={true}

        /> */}
        {landingPageResponse.alumni_status_cards?.length>0 &&  
          <div className={`${classes.light_bg} p-0 `}>
                      <div className={`mx-auto ${classes.equalPadding}  py-4`}>
                        <div className={` m-0 py-3`}>
                          <div  className={`text-center`}>
      
                            <h2 className={`text-center ${classes.main_blue_head}`}>{landingPageResponse.alumni_status_title}</h2>
                          </div>
                          <div className={`${classes.first_div}  `}>
                        <div className={`row px-4`}>
                          <Slider {...settings1}>
                            {landingPageResponse.alumni_status_cards.slice(0, 3).map((card, i) => (
                            <div key={i} >
                              <Card lpv2flag={true} content={card.description} backgroundImage={`${props.apiUrl}${card.image?.data?.attributes?.formats?.thumbnail?.url}`} />
                            </div>
                            ))}
                          </Slider>
                    </div>
                    </div>
                  <div className={`${classes.second_div} px-md-4 px-0 `}>
                    <CardContainer lpv2flag={true} cards={cards} />
      
                  </div>
                        </div>
                        {/* <div className=" d-flex justify-content-center">
                          <button
                            onClick={scrollToTop}
                            type="button"
                            className={`${classes.land_btn} btn btn-primary my-3 `}
                          >
      
                            Download Brochure
                          </button>
                        </div> */}
                      </div>
                     
          </div>
      }
      <div className={`${classes.light_bg} bg-white p-0 `} >
      <div className={`mx-auto ${classes.equalPadding}  py-4`}>
      {landingPageResponse.what_sets_us_apart&& <div className="" >
        <OnlineAdvantage lpflag={true} apiUrl={props.apiUrl}  />
      </div>}
      </div>
      </div>



        <IsbAdvantage landingPageResponse={landingPageResponse} classes={classes} />
        {/* <div className=" d-flex justify-content-center py-3 bg-white">
          <DownloadBrocureBtn mobile={true} thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse} />

        </div> */}
      <PaymentModal landingPageResponse={landingPageResponse}  handleClosePaymentModal={handleClosePaymentModal} showPaymentModal={showPaymentModal} />
      <FinancingOptionModal 
         
        handleClosePaymentModal={handleClosePaymentModal} 
        showFinancOptionsModal={showFinancOptionsModal} 
        financingData={financingData}
        financingLoading={financingLoading}
        financingError={financingError}

      />
      <CustomModal buttonDisabled={buttonDisabled} 
              URL={url}
              tags={tags}
              groupEnrolment={showGroupEnrolment} 
              thankYouModal={thankYouModal}
              landingPageResponse={landingPageResponse} 
              showGroupEnrolment={showGroupEnrolment} 
              handleDownload={handleDownload} 
              classes1={classes1} classes={classes} 
              handleClosePaymentModal={handleClosePaymentModal} 
              />
       
        <Image
                    className="img-fluid w-100 mt-4"
                    src={banner}
                    alt="banner_image"
                  />
        <TrackPageBottomFold apiUrl={props.apiUrl} />
        {/* <div className={`mt-5 ${classes.showBanner}`}></div> */}
      </>}
    </>
  );
}

export const getStaticPaths = async (context) => {

  const APIUrl = process.env.API_BASE_URL;

  const res = await fetch(`${APIUrl}/api/landing-pagev2s`);
  const response = await res.json();
 
  const paths = response.data.map((learningTrack) => {
   
    return {
      params: {
        learning_track_id:
          learningTrack.attributes.learning_track_id.toString(),
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  const meetupId = context.params.learning_track_id;

  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;

  const [
    stickyBanner,
    tracklandingpage, 
    trackFold,
    learningTrackDataV1
   
  ] = await Promise.all([
    fetch(`${APIUrl}/api/sticky-banners?populate=deep,4`).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/landing-pagev2s?filters[learning_track_id][$eq]=${meetupId}&populate=deep,5`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-page-fold?populate[apply_to_lt_title][populate]=*`).then((r) => r.json()),


    // v1 api call

    fetch(
      `${APIUrl}/api/learning-tracks?filters[learning_track_id][$eq]=${meetupId}&populate=[isV2][populate]`
    ).then((r) => r.json()),
     
  ]);
  return {
    props: {
      stickyBanner: stickyBanner,
      apiData: tracklandingpage,
      trackFold: trackFold,
      apiUrl: APIUrl,
      
      baseurl: Baseurl,
      meetupId:
        tracklandingpage.data[0].attributes.new_track_page.data.attributes.learning_track_id,

        learningTrackDataV1:learningTrackDataV1
    },
    revalidate: 120,
  };
}



export const PaymentModal=({landingPageResponse, handleClosePaymentModal, showPaymentModal})=>{
  return(
    <div id="myModal" className={`${classes.fin_modal} d-${showPaymentModal ? "flex":"none"}`}>
        <div  className={`${classes.modal_content}`} style={{maxWidth:showPaymentModal?"900px":""}}>
       
       <div className={classes.payment_plan_div}>
        <div className="d-flex justify-content-between">
        
          <h2 className=" ">{landingPageResponse.Payment_plan_title}</h2>
              <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
        </div>
            <div className={classes.paymodal_body}>
              <div className={` `}>
                <div className={`${classes.programfee}`}>{landingPageResponse.Programme_fee_title}</div>
                <div className={classes.admin_price}>{landingPageResponse.Programme_fee}</div>
                <div className={classes.gst_text}>{landingPageResponse.Payment_plan_description}</div>

                {landingPageResponse.payment_plan.map((pay, index) =>{
                  return (
                    <div key={index} className={classes.payment_table_wrpr}>
                      <h6>{pay.PaymentScheduleTitle}</h6>
                      <table className={`${classes.table_style} table`}>
                        <thead>
                          <tr>
                            <th scope="col" className={` ${classes.bg_gray}`}>
                              {pay.payment_date_title}
                            </th>
                            <th scope="col" className={` ${classes.bg_gray}`}>
                              {pay.amount_due_title}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                        {pay.Particulars.map((par, index)=>{
                          return(
                            <tr key={index}>
                            <td>{par.Date}</td>
                            <td>{par.Fee}</td>
                          </tr> )}) }
                        </tbody>
                      </table>
                    </div>
                  )
                })
                }

              </div>
            </div>
      </div> 

        </div>

      </div>
  )
}

const FinancingOptionModal=({ showFinancOptionsModal, handleClosePaymentModal, financingData, financingLoading, financingError})=>{
  return(
    <div id="myModal" className={`${classes.fin_modal} d-${showFinancOptionsModal ? "flex":"none"}`} >
      <div className={classes.modal_content}>
        <div className="financing_optns_div">
          <div className="d-flex justify-content-between">
            <h2>{financingData.finance_options_title}</h2>
            <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
          </div>
          <div id="modal_content" className={classes.paymodal_body}>
            <div className={`${classes.contentext} container-md pt-4 pb-3 pb-md-4`}>
              <DataTable 
                headers={financingData && financingData.header ? financingData.header : []}
                data={financingData && financingData.data ? financingData.data : []}
                loading={financingLoading}
                error={financingError}
                emptyMessage="No financing institutions found. Please check back later."
                className={classes1.financeTable}
                responsive={true}
                description={financingData.description}
                footer={
                  <p className="mb-0">{financingData.footer_text ? (
                    financingData.footer_text.includes('@') ? (
                      <>
                        {financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}
                        <a href={`mailto:${financingData.footer_text.match(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}`} className='text-decoration-none'>
                          {financingData.footer_text.match(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}
                        </a>
                        {financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/).length > 1 ? 
                          financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[1] : ''}
                      </>
                    ) : financingData.footer_text
                  ) : 'For any further clarifications, you may write to us at '}
                  {!financingData.footer_text || !financingData.footer_text.includes('@') ? 
                    <a href="mailto:<EMAIL>" className='text-decoration-none'><EMAIL></a> : null
                  }
                  </p>
                }
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const FinanceLinks=({lpthankyoupageflag, handleShowPaymentModal, landingPageResponse})=>{
return(
  <>
     <a className={`${classes.financing_options_link}`} id={"payment_modal"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.lt_data.view_paymet_plan}</a><br/>
      <a className={`${classes.financing_options_link}`} id={"financing_options"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.lt_data.enterprise_pricing}</a><br/>
      {!lpthankyoupageflag && <a className={`${classes.financing_options_link}`} id={"group_enrolment"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.group_enrolment?.ge_link_title}</a>}
  </>
)
}
const DownloadBrocureBtn=({scrollToTop, thankyouScreen,handleDownload,landingPageResponse, mobile})=>{
  return(
    <>
      {thankyouScreen ? (
              <button
                className={`${classes.nav_button} text-white btn m-0`}
                type="button"
                onClick={handleDownload}
              >
                {landingPageResponse.header_and_sticy_cta}
              </button>
            ) : (
              <Link href="#form" scroll={false}>
               
                  <button
                  onClick={scrollToTop}
                    className={`${classes.nav_button} text-white btn m-0`}
                    type="submit"
                  >
                    {landingPageResponse.header_and_sticy_cta}
                  </button>
                
              </Link>
            )}
    </>
  )
}


export const VideoSection =({
  autoplayMobile, bannerFullData, learnTrackData, autoplay, handleImageClick, landingPageResponse,handleImageClick1,showIframe, apiUrl, isImageHidden

})=>{
  return(
    <div className={`row m-0 mx-auto ${classes.equalPadding} pt-4 pb-0`}>

    <div className={`${classes.container_main} h-50 col col-lg-5 col-md-12 p-0`}>
      <Image className={`${classes.h_w} `} src={group} alt="backgroundImage"></Image>
<>
{
              !showIframe ? (
                <div className={`${classes.first_div_landing} d-lg-none d-xl-block `} style={{maxHeight:"250px"}}>
                  <div className={`${classes.centered_image} top-50`}>
                    <Image
                      onClick={handleImageClick1}
                      width={100}
                      height={100}
                      priority={true}
                      alt="Iframe_Image"
                      sizes="100vw"
                      style={{ width: "100%", height: "auto" }}
                      src={`${apiUrl}${landingPageResponse?.Iframe_fold?.Iframe_Image.data.attributes.formats.large.url}`}
                    ></Image>

                    <div className={classes.playIcon}>
                      {landingPageResponse?.Iframe_fold?.Iframe_url && <Image alt="playicon" priority={true} onClick={handleImageClick1} height={62} width={62} src={playIcon}></Image>}
                    </div>
                  </div>
                </div>
              ) : (
                <div className={`${classes.centered} ${classes.first_div_landing} top-50`}>
                  {/* <iframe
                    className={`${classes.centered} `}
                    width="100%"
                    height="100%"
                    src={landingPageResponse?.Iframe_fold?.Iframe_url}
                    allowFullScreen={true}
                    allow="autoplay; fullscreen; picture-in-picture"
                  ></iframe> */}
                  <ReactPlayer
          url={landingPageResponse?.Iframe_fold?.Iframe_url}
          className="react-player"
          playing ={autoplayMobile}
          width='100vw'
          height='100%'
          controls={true}
        />
                </div>
              )
            }



    <div className={` ${classes.second_div_landing}  `}>
       <div className={`${classes.centered_image} top-50 `} >
        <div className={`${classes.video_box} `}>
        <div className={`${classes.video_overlays} `}>
          {!isImageHidden && (
            <>
      <div className={`${!landingPageResponse?.Iframe_fold?.Iframe_url && classes.lp_centered_image}`} style={{ position: 'fixed', width: '100%', height: '100%'}}>
              <Image
                onClick={handleImageClick}
                width={0}
                height={0}
                priority={true}
                sizes="100vw"
                alt="Iframe_Image"
                style={{ width: '100%', height: 'auto'}}
                src={`${apiUrl}${landingPageResponse?.Iframe_fold?.Iframe_Image.data.attributes.formats.large.url}`}
              />
              <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1 }}>
                {landingPageResponse?.Iframe_fold?.Iframe_url && <Image alt="" priority={true} onClick={handleImageClick} height={62} width={62} src={playIcon} />}
              </div>

      </div>
            </>  
          )}
      </div>
        <div>
         
          <ReactPlayer
          url={landingPageResponse?.Iframe_fold?.Iframe_url}
          className="react-player"
          playing ={autoplay}
          width='100%'
          height='100%'
          controls={true}
        />
        </div>
        </div>
       </div>
    </div>
       </>   
    </div>
    <div className={`${classes.lpkey_concepts} col px-0`}> 
    <KeyConcepts 
       divbsclasses={`${classes.equalPadding} col-lg-12 ps-3 ms-lg-3 me-lg-0 me-2 pe-2 mt-md-0 mt-4`}
       headingClass={`${classes.sideHeads} lh-1 text-center text-lg-start`}
       ulclass ={`d-flex row me-lg-0 mx-0 p-md-4 pt-md-2 p-3 pe-2 gap-3 gap-lg-2 ${classes.lpkeyconceptsul}`}
       liclass={`${classes.lpcustombulleticon}  `}
       arrayData={learnTrackData}
       title={bannerFullData.key_concepts_title}
        />
    </div>
  </div>
  )
}