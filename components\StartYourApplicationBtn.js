import React, { useEffect, useState } from 'react'

export const StartYourApplicationBtn = ({fullUrl,landingPageResponse,classes,learnTrackData,qpms,andQpms,baseURL,trackFoldData}) => {

  const [queryParams, setQueryParams] = useState('');
 
useEffect(() => {
  if (typeof window !== 'undefined') {
    const qParams = window.location.search.slice(1); 
    setQueryParams(`&${qParams}`);
  }
}, []);

  return (
    <div className="text-center mt-1">
    {process.env.NEXT_PUBLIC_BASE_URL === `https://online.isb.edu` ? ((learnTrackData ? learnTrackData.learning_track_url_prod : landingPageResponse.learning_track_url_prod) != null ? <a className="text-decoration-none" 
      href={`${learnTrackData ? learnTrackData.learning_track_url_prod : landingPageResponse.learning_track_url_prod}?url=${baseURL
        }@!${learnTrackData ? learnTrackData.learning_track_id : landingPageResponse.learning_track_id}${qpms != `` ? queryParams  : ``
        }${fullUrl}`}
    >
      <p className={` text-decoration-none btn `}  style={{backgroundColor:'var(--isb-blue-color)', padding:"8px 24px", color:"white", borderRadius:"0", border:"none", marginBottom:"0"}}>
        {trackFoldData.apply_to_lt_title}
      </p>
    </a> : <div></div>) : ((learnTrackData? learnTrackData.learning_track_url_staging : landingPageResponse.learning_track_url_staging)!= null ? <a className="text-decoration-none"
      href={`${learnTrackData ? learnTrackData.learning_track_url_staging : landingPageResponse.learning_track_url_staging}?url=${baseURL
        }@!${learnTrackData ? learnTrackData.learning_track_id : landingPageResponse.learning_track_id}${qpms != `` ? queryParams  : ``
        }${fullUrl}`}
    >
      <p className={` text-decoration-none btn `} style={{backgroundColor:'var(--isb-blue-color)', padding:"8px 24px", color:"white", borderRadius:"0", border:"none", marginBottom:"0"}}>
        {trackFoldData.apply_to_lt_title}
      </p>
    </a> : <div></div>)}
  </div>
  )
}
