import React, { useEffect, useState, useMemo } from 'react';
import Image from 'next/image';
import Head from 'next/head';
import styles from './article.module.css';
import { useRouter } from 'next/router';
import Breadcrumb from '../../../components/Breadcrumb/Breadcrumb';
import Link from 'next/link';
import TrackPageBottomFold from '../../../components/TrackPageBottomFold';
import FacultySection from '../../../components/FacultySection';
import {RecommendedForYou, ProgeammesForYou, ShareArticle, TableOfContents, TopicsComponentForSlug } from '../../../components/PerspectiveComponents';
import playicon from '../../../assets/playicon.svg';
import dynamic from 'next/dynamic';
import LeadForm  from '../../../components/EdgeLeadForm/index';
import TopicsDropdown from '../../../components/TopicsDropdown';
import { NextSeo } from 'next-seo';
import Script from 'next/script';

const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });

export default function ArticlePage({ articlesData, apiUrl, baseurl, leadFormData, perspectivesMetaData }) {
  const router = useRouter();
  const {  query } = router;
  const { slug } = router.query;
  const [activeSection, setActiveSection] = useState('');
  const [scrollProgress, setScrollProgress] = useState(0);
  const apiPosts = articlesData.data[0].attributes?.Recommended_post.data  || [];
  const Headline_text = articlesData.data[0].attributes.Headline_text;
  const Topics = articlesData.data[0].attributes.topics.data;
  const Post_type = articlesData.data[0].attributes.post_first_section.post_type;
  const post_first_section = articlesData.data[0].attributes.post_first_section;
  const articles_body = articlesData.data[0].attributes.articles_body;
  const professor_section = articlesData.data[0].attributes.professor?.data?.attributes;
  const track_details = articlesData.data[0]?.attributes?.new_track_pages?.data;
  const shareArticle = articlesData.data[0].attributes.Share_section;  
  const articleMetaData = articlesData.data[0].attributes.meta_tags;

  const [showFullSummary, setShowFullSummary] = useState(false);
  const [navData, setProgrammes] = useState(null);

  // Calculate reading time
  const calculateReadingTime = (content) => {
    if (!content) return 0;
    // Remove HTML tags and trim whitespace
    const plainText = content.replace(/<[^>]*>/g, '');
    // Count words (split by whitespace)
    const wordCount = plainText.trim().split(/\s+/).length;
    // Average reading speed (words per minute)
   
    const wordsPerMinute = 200;


    // Calculate reading time in minutes
    return wordCount / wordsPerMinute;
  };

  // Calculate total reading time from all sections
  const totalReadingTime = useMemo(() => {
    let totalTime = 0;


    
    if (articles_body && articles_body.length > 0) {
      articles_body.forEach(section => {
        if (section.description) {
          totalTime += calculateReadingTime(section.description);
        }
      });
    }
    const sectionTime ={
       totalTime: Math.ceil(totalTime),
       title: articlesData.data[0].attributes.post_id
    }

    return totalTime;
  }, [ articles_body]);


  const readTimePermin = Math.ceil( totalReadingTime);


  const currentQuery = router.asPath.includes("?") ? router.asPath.split("?")[1] : "";


  const qpms =
  query.utm_source != undefined
    ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}`
    : ``;

    
    const transformHTML = (htmlString) => {
      if (!htmlString) return "";
      htmlString = htmlString.replace(/<img([^>]+)src="([^"]+)"([^>]*)>/g, (match, beforeSrc, src, afterSrc) => {
        const fullSrc =  `${apiUrl}${src}`;
        return `<Image src="${fullSrc}" alt="Image" width=100% height=auto layout="responsive" sizes=${'100vw'}/>`;
        
      });
      htmlString = htmlString.replace(/<a\s(?![^>]*\btarget=)[^>]*>/g, (match) => {
        return match.replace("<a", '<a target="_blank" rel="noopener noreferrer"');
      });
    
      return htmlString;
    };

  const combinedArray = [
    ...apiPosts.map(post => ({
      id: post.id,
      attributes: {
        ...post.attributes.post_first_section,
        post_id : post.attributes.post_id
      }
    })),  
  ];


  useEffect(() => {
    const updateProgress = () => {
      const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
      const windowHeight = scrollHeight - clientHeight;
      const progress = (scrollTop / windowHeight) * 100;
      setScrollProgress(progress);
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, {
      rootMargin: '-20% 0px -35% 0px'
    });

    document.querySelectorAll('section[id]').forEach((section) => {
      observer.observe(section);
    });

    window.addEventListener('scroll', updateProgress);
    updateProgress();

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', updateProgress);
    };
  }, []);
  useEffect(() => {
    fetchData()
     
  }, [])





  const truncateText = (text, limit) => {
    if (!text) return '';
    if (text.length <= limit) return text;
    return text.slice(0, limit - 3) + '...';
  };
  const breadcrumbItems = [
    { label: 'Perspectives', href: '/perspectives' },
    { label: 'Article', href: '/perspectives/alltopics?type=Article' },
    {
      label: truncateText(articlesData.data[0].attributes.post_first_section.post_name, 68),
      href: '#'
    }
  ];
 

  if (router.isFallback) {
    return <div>Loading...</div>;
  }


  const toggleSummary = () => {
    setShowFullSummary(!showFullSummary);
  };

  const truncatedSummary = post_first_section.summary?.length > 150
    ? `${post_first_section.summary.slice(0, 150)}...`
    : post_first_section.summary;
  
    const fetchData = async () => {
      const url = process.env.NEXT_PUBLIC_API_BASE_URL;
      const res = await fetch(`${url}/api/navbars?populate=deep,4`);
      const data = await res.json();
      setProgrammes(data?.data[0].attributes);
  
    };
  const navbarData = navData && navData;

  if (!articlesData) {
    return null;
  }  // Configure meta tags using article-specific data
  const metaTitle = articleMetaData?.title || post_first_section.post_name || 'ISB Online Article';
  const metaDescription = articleMetaData?.description || truncatedSummary || post_first_section.short_summary || 'Read this insightful article from ISB Online';
  const metaKeywords = [
    articleMetaData?.keywords,
    ...Topics?.map(topic => topic.attributes?.topic_name) || [],
    'ISB Online',
    'ISB Article',
    Post_type
  ].filter(Boolean).join(', ');
  const metaRobots = articleMetaData?.robots || 'index, follow';
  const metaImage = articleMetaData?.image?.data?.attributes?.url 
    ? `${apiUrl}${articleMetaData.image.data.attributes.url}`
    : post_first_section.slug_image?.data?.attributes?.url 
      ? `${apiUrl}${post_first_section.slug_image.data.attributes.url}`
      : `${process.env.NEXT_PUBLIC_BASE_URL}/images/isb_logo_new.png`;
      
  return (
    <>     
          <Script
        id="article-schema-markup"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Article',
            headline: post_first_section.post_name,
            image: [
              metaImage
            ],
            datePublished: post_first_section.posted_date,
            dateModified: post_first_section.updatedAt || post_first_section.posted_date,
            author: {
              '@type': 'Person',
              name: post_first_section.author_name || 'ISB Online'
            },
            publisher: {
              '@type': 'Organization',
              name: 'ISB Online',
              logo: {
                '@type': 'ImageObject',
                url: `${process.env.NEXT_PUBLIC_BASE_URL}/images/isb_logo_new.png`
              }
            },
            description: metaDescription,
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': `${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`
            },
            articleSection: Post_type || 'Article',
            wordCount: readTimePermin * 200, // Approximate based on reading time
            keywords: Topics?.map(topic => topic.attributes?.topic_name) || [],
            articleBody: articles_body?.map(section => section.title + " " + section.description?.replace(/<[^>]*>/g, '')).join(" ")
          })
        }}
      />
       <NextSeo
        title={metaTitle}
        canonical={`${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`}
        description={metaDescription}
        openGraph={{
          type: 'article',
          url: `${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`,
          title: metaTitle,
          description: metaDescription,
          locale: 'en-IN',          images: [
            {
              url: metaImage,
              alt: articleMetaData?.image?.data?.attributes?.alternativeText || post_first_section.post_name || 'Article Image',
              width: articleMetaData?.image?.data?.attributes?.width || post_first_section.slug_image?.data?.attributes?.width || 1200,
              height: articleMetaData?.image?.data?.attributes?.height || post_first_section.slug_image?.data?.attributes?.height || 630,
            },
          ],
          site_name: 'ISB Online',
          article: {
            publishedTime: post_first_section.posted_date,
            modifiedTime: post_first_section.updatedAt,
            section: Post_type || 'Article',
            authors: [post_first_section.author_name].filter(Boolean),
            tags: Topics?.map(topic => topic.attributes?.topic_name) || [],
            publisher: {
              '@type': 'Organization',
              name: 'ISB Online',
              logo: {
                '@type': 'ImageObject',
                url: `${process.env.NEXT_PUBLIC_BASE_URL}/images/isb_logo_new.png`
              }
            }
          }
        }}        
        twitter={{
          handle: '@ISBOnline',
          site: '@ISBOnline',
          cardType: 'summary_large_image',
          title: metaTitle,
          description: metaDescription,
          image: {
            url: metaImage,
            alt: articleMetaData?.image?.data?.attributes?.alternativeText || post_first_section.post_name || 'Article Image'
          }
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: metaKeywords
          },
          {
            name: 'robots',
            content: metaRobots
          },
          {
            name: 'article:author',
            content: post_first_section.author_name || ''
          },
          {
            name: 'article:published_time',
            content: post_first_section.posted_date || ''
          },
          {
            name: 'article:section',
            content: Post_type || 'Article'
          }
        ]}
      />
      
      <div style={{ position: 'relative', zIndex: 1000, width: '100%' }}>
       <TopicsDropdown />
     </div>
             
      <div style={{backgroundColor:"#F4F8FA"}}>
      <div className="container">
      <Breadcrumb items={breadcrumbItems} headlinetext={Headline_text}/>
    </div>
        <article className={styles.article}>
         <p className={styles.posteddate}>{post_first_section.posted_date}</p>
          <div className={styles.container}>
            <div className={styles.mainContent}>
              <header className={styles.header}>
                {post_first_section.slug_image_required && <div className={styles.headerImage} >
                  <Image
                    src={`${apiUrl}${post_first_section.slug_image.data.attributes.url}`}
                    alt={post_first_section.slug_image.data.attributes.alternativeText}
                    width={0}
                    height={0}
                    sizes='100vw'
                    priority
                    className={styles.image}
                  />
                </div>}

                
                <div className={styles.headerContent}>

                <div className={`${styles.summarymain} d-flex justify-content-between align-items-center mt-2 mt-md-4`}>
                  {/* <p className={styles.subtext}>{post_first_section.subtext}</p> */}
                  <TopicsComponentForSlug Topics={Topics} Post_type={Post_type}/>
                  <div className="d-flex justify-content-end">
                  <div className={`${styles.categoryIcon} `}>
                  <Image width={20} height={20} src={`${apiUrl}${post_first_section.category_icon.data.attributes.url}`} alt="Category Icon" />
                  </div>
                  </div>
                </div>



                  <h1>{post_first_section.post_name}</h1>
                {post_first_section.summary &&  <p className={styles.description}>
                    <b>Summary.</b>{' '}
                    {post_first_section.summary?.length > 150 ? (
                      <>
                          {showFullSummary ? post_first_section.summary : truncatedSummary}{' '}
        
                          <span
                            onClick={toggleSummary}
                            className={`${styles.readMoreBtn} pointer text-decoration-underline`}
                          >
                            {showFullSummary ? 'Read Less' : 'Read More'}
                          </span>
                      </>
                    ) : (
                      post_first_section.summary
                    )}
                  </p>}
                  {/* <div className={styles.articleMeta}>
                    
                    <span>{readTimePermin}</span>
                  </div> */}
                </div>
              </header>
              <div className={styles.content}>
                {articles_body.map((section, index) => {
                  
                  return(
                  <section
                    key={index}
                    id={`section-${index}`}
                    className={styles.section}
                  >
                    <h2>{section.title}</h2>

                    <div className={styles.descriptiondiv} dangerouslySetInnerHTML={{
                      __html: transformHTML(section.description)
                    }} />

                    {section.video_url && (
                      <div className={styles.videoWrapper}>
                      
                        {/* <iframe
                          width="100%"
                          height="400"
                          src={section.video_url}
                          title={section.title}
                          frameBorder="0"
                          allow="accelerometer;  clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                          className='d-none d-md-block'
                        /> */}
                        <ReactPlayer
                            wrapper="div"
                            className='d-none d-md-flex'
                            // style={{ minHeight: '390px' }}
                            allow="accelerometer; fullscreen; picture-in-picture"
                            url={section.video_url||''}
                            width={'100%'}
                            height={390}
                            playing={true}
                            controls={true}
                            preload={true}
                            light={true}
                            playIcon={<Image src={playicon} alt="Play" width={70} height={70} />}
                          />
                          
                          <ReactPlayer
                            wrapper="div"
                            className={`col-lg-6 col-md-12 p-0 d-md-none ${styles.mobileiframeWrapper}`}
                            style={{ minHeight: '205px' }}
                            url={section.video_url}
                            width={'100%'}
                            height={260}
                            allowFullScreen={true}
                            allow="fullscreen; picture-in-picture"
                            light={true}
                            controls={true}
                            playing={true}
                            preload={true}
                            playIcon={<Image src={playicon} alt="Play" width={50} height={50} />}

                          />
                      </div>
                    )}
                    {section.image?.data && (
                      <div className={styles.sectionImage}>
                        <Image
                          src={`${apiUrl}${section.image.data.attributes.url}`}
                          alt={section.image.data.attributes.alternativeText || section.title}
                          width={670}
                          height={403}
                          className={styles.image}
                        />
                      </div>
                    )}
                    {section.pdf_url && (
                      <div className={styles.pdfLink}>
                        <a href={section.pdf_url} target="_blank" rel="noopener noreferrer">
                          Download PDF
                        </a>
                      </div>
                    )}
                      {section.audio_url && (
                        <div className={styles.audioPlayer}>
                          <audio controls>
                            <source src={` ${section.audio_url}`} type="audio/mpeg" />
                          </audio>
                        </div>
                      )}

                  </section>
                )})}
              </div>
            </div>
            <aside>
              <div className={styles.asidemain} style={{top:professor_section && shareArticle.length>0 ? "0.5rem" : professor_section && shareArticle.length==0 ? "6.6rem" : "8rem"}}>
              {shareArticle.length>0 && <ShareArticle styles={styles} shareArticle={shareArticle} apiUrl={apiUrl} title={articlesData.data[0].attributes.Share_article_title}/>}
              {professor_section && <FacultySection shareArticle={shareArticle} professor_section={professor_section} apiUrl={apiUrl}/>}
              <TableOfContents styles={styles} articles_body={articles_body} activeSection={activeSection}  title={articlesData.data[0].attributes.table_content_title}/>
                <div className={styles.progressWrapper}>
                  <div
                    className={styles.progress}
                    style={{ width: `${scrollProgress}%` }}
                  />
                <div>
                  <p className='text-end mt-2'>{`${readTimePermin}  MIN READ`}</p>
                </div>
                </div>
                <div >
                 {track_details.length>0 && <h4 className={styles.programmesforyou}>
                  {articlesData.data[0].attributes.programme_for_you_title}
                  </h4>}
                 
                  <ProgeammesForYou track_details={track_details} apiUrl={apiUrl} currentQuery={currentQuery} />
                  
                </div>
              </div>
            </aside>
          </div>
        </article>

        {/* <div className={styles.leadFormWrapper}>
          <LeadForm leadFormData={leadFormData} apiUrl={apiUrl} />
        </div> */}

        <RecommendedForYou date={post_first_section.date}  readTimePermin={readTimePermin} combinedArray={combinedArray} apiUrl={apiUrl} currentQuery={currentQuery} router={router} title={articlesData.data[0].attributes.Recommended_title} />
      </div>
        <TrackPageBottomFold navData={navbarData} faqsection={false} apiUrl={apiUrl} />
    </>
  );
}


export const getStaticPaths = async () => {
  const APIUrl = process.env.API_BASE_URL;
    const res = await fetch(`${APIUrl}/api/articles?populate=deep,10`);
    const response = await res.json();
    const paths = response.data.map((article) => {
      const slug = String(article.attributes.post_id);
      return {
        params: {
          slug: slug
        }
      };
    });

    return {
      paths,
      fallback: 'blocking'
    };
};

export async function getStaticProps({ params }) {
  const { slug } = params;
  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;

  const [articlesRes, leadFormRes, metaRes] = await Promise.all([
    fetch(`${APIUrl}/api/articles?filters[post_id][$eq]=${slug}&populate=deep,4`),
    fetch(`${APIUrl}/api/edge-leadform?populate=deep,3`),
    fetch(`${APIUrl}/api/edge-main-page?populate=meta_tags.image`)
  ]);

  const [articlesData, leadFormData, metaTagsData] = await Promise.all([
    articlesRes.json(),
    leadFormRes.json(),
    metaRes.json()
  ]);

  return {
    props: {
      articlesData: articlesData,
      leadFormData: leadFormData,
      perspectivesMetaData: metaTagsData,
      apiUrl: APIUrl,
      baseurl: Baseurl,
    },
    revalidate: 120,
  };
}