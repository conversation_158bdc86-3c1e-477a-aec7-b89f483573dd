import React, { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import Head from 'next/head';
import styles from './podcast.module.css';
import { useRouter } from 'next/router';
import Breadcrumb from '../../../components/Breadcrumb/Breadcrumb';
import Link from 'next/link';
import TrackPageBottomFold from '../../../components/TrackPageBottomFold';
import AudioPlayer from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';

// import forwardicon from '../../../assets/Forward 10.png'
// import backwardicon from '../../../assets/Replay 10.png'
import  {CustomAudioPlayer} from '../../../components/CustomAudioPlayer';
import LeadForm from '../../../components/EdgeLeadForm';
import { ProgeammesForYou, ShareArticle, TopicsComponentForSlug } from '../../../components/PerspectiveComponents';
import FacultySection from '../../../components/FacultySection';
import TopicsDropdown from '../../../components/TopicsDropdown';
import { NextSeo } from 'next-seo';
import Script from 'next/script';

export default function PodcastPage({ articlesData, apiUrl, baseurl }) {

 
  const router = useRouter();
  const { query } = router;
  const { slug } = router.query;
  const [activeSection, setActiveSection] = useState('');
  const [scrollProgress, setScrollProgress] = useState(0);
  // const apiPosts = articlesData.data[0].attributes?.podcasts.data || [];
  const [mailId, setMailId] = useState("")
  const Headline_text = articlesData.data[0].attributes.Headline_text;
  const post_first_section = articlesData.data[0].attributes.podcaste_first_section;
  const audioSection = articlesData.data[0].attributes.audio_section;
  const Topics = articlesData.data[0].attributes?.topics?.data;
  const Post_type = articlesData.data[0].attributes.podcaste_first_section.post_type;
  const professor_section = articlesData.data[0].attributes.professor.data.attributes;
  const track_details = articlesData.data[0]?.attributes?.new_track_pages?.data;
  const shareArticle = articlesData.data[0].attributes.Share_section;
  const podcastMetaData = articlesData.data[0].attributes.meta_tags
  const [showFullSummary, setShowFullSummary] = useState(false);
  const [navData, setProgrammes] = useState(null);
  const [mounted, setMounted] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef(null);

  const currentQuery = router.asPath.includes("?") ? router.asPath.split("?")[1] : "";

  const qpms =
    query.utm_source != undefined
      ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}`
      : ``;



  // const combinedArray = [
  //   ...apiPosts.map(post => ({
  //     id: post.id,
  //     attributes: {
  //       ...post.attributes.info_first_section,
  //       post_id: post.attributes.post_id
  //     }
  //   })),


  // ];


  useEffect(() => {
    const updateProgress = () => {
      const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
      const windowHeight = scrollHeight - clientHeight;
      const progress = (scrollTop / windowHeight) * 100;
      setScrollProgress(progress);
     
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, {
      rootMargin: '-20% 0px -35% 0px'
    });

    document.querySelectorAll('section[id]').forEach((section) => {
      observer.observe(section);
    });

    window.addEventListener('scroll', updateProgress);
    updateProgress();

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', updateProgress);
    };
  }, []);
  useEffect(() => {
    fetchData()

  }, [])
  useEffect(() => {
    setMounted(true);
  }, []);

   const truncateText = (text, limit) => {
    if (!text) return '';
    if (text.length <= limit) return text;
    return text.slice(0, limit - 3) + '...';
  };
  
  const breadcrumbItems = [
    { label: 'Perspectives', href: '/perspectives' },
    { label: 'Podcast', href: '/perspectives/alltopics?type=Podcast' },
    { label: truncateText(articlesData.data[0].attributes?.podcaste_first_section?.post_name, 68), href: '#'}
  ];

  const getIsbMail = (mail) => {
    setMailId(mail)
  }

  if (router.isFallback) {
    return <div>Loading...</div>;
  }


  const toggleSummary = () => {
    setShowFullSummary(!showFullSummary);
  };

  const truncatedSummary = post_first_section.summary?.length > 150
    ? `${post_first_section.summary.slice(0, 150)}...`
    : post_first_section.summary;

  const fetchData = async () => {
    const url = process.env.NEXT_PUBLIC_API_BASE_URL;
    const res = await fetch(`${url}/api/navbars?populate=deep,4`);
    const data = await res.json();
    setProgrammes(data?.data[0].attributes);

  };
  const navbarData = navData && navData;

  if (!articlesData) {
    return null;
  }

  const handleForward10 = () => {
    if (audioRef.current) {
      audioRef.current.audio.current.currentTime += 10;
    }
  };

  const handleBackward10 = () => {
    if (audioRef.current) {
      audioRef.current.audio.current.currentTime -= 10;
    }
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSliderChange = (e) => {
    const time = parseFloat(e.target.value);
    setCurrentTime(time);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  };


  // Meta tags configuration
  const metaTitle = podcastMetaData?.title || 'ISB Online';
  const metaDescription = podcastMetaData?.description || 'View this insightful infographic from ISB Online';
  const metaKeywords = [
    podcastMetaData?.keywords,
    ...Topics?.map(topic => topic.attributes?.topic_name) || [],
    'ISB Online',
    'ISB Article',
    Post_type
  ].filter(Boolean).join(', ');
  const metaRobots = podcastMetaData?.robots || 'index, follow';
  const metaImage = podcastMetaData?.image?.data?.attributes?.url 
    ? `${apiUrl}${podcastMetaData.image.data.attributes.url}`
    : post_first_section.slug_image?.data?.attributes?.url 
      ? `${apiUrl}${post_first_section.slug_image.data.attributes.url}`
      : `${process.env.NEXT_PUBLIC_BASE_URL}/images/isb_logo_new.png`;

  return (
    <>
      <Script
        id="podcast-schema-markup"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'PodcastEpisode',
            name: post_first_section.post_name,
            datePublished: post_first_section.posted_date,
            dateModified: post_first_section.updatedAt || post_first_section.posted_date,
            description: metaDescription,
            image: [
              metaImage
            ],
            associatedMedia: {
              '@type': 'AudioObject',
              contentUrl: audioSection?.audio?.data?.attributes?.url 
                ? `${apiUrl}${audioSection.audio.data.attributes.url}` 
                : '',
              encodingFormat: 'audio/mpeg',
              name: post_first_section.post_name
            },
            author: {
              '@type': 'Person',
              name: professor_section?.professor_name || post_first_section.author_name || 'ISB Online'
            },
            publisher: {
              '@type': 'Organization',
              name: 'ISB Online',
              logo: {
                '@type': 'ImageObject',
                url: `${process.env.NEXT_PUBLIC_BASE_URL}/images/isb_logo_new.png`
              }
            },
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': `${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`
            },
            keywords: Topics?.map(topic => topic.attributes?.topic_name) || [],
            duration: audioSection?.duration || ''
          })
        }}
      />
      {/* <Head>
        <title>{`${articlesData.data[0].attributes.podcaste_first_section.post_name} | ISB Executive Education`}</title>
      </Head> */}
      <NextSeo
              title={metaTitle}
              canonical={`${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`}
              description={metaDescription}
              openGraph={{
                type: 'article',
                url: `${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`,
                title: metaTitle,
                description: metaDescription,
                locale: 'en-IN',
                images: [
                  {
                    url: metaImage,
                    alt: post_first_section.post_name || 'infographic image',
                  },
                ],
                site_name: 'ISB Online',
                article: {
                  publishedTime: post_first_section.posted_date,
                  authors: [post_first_section.author_name],
                  tags: Topics?.map(topic => topic.attributes?.topic_name) || []
                }
              }}
              twitter={{
                handle: '@ISBOnline',
                site: '@ISBOnline',
                cardType: 'summary_large_image',
                title: metaTitle,
                description: metaDescription,
                image: metaImage,
              }}
              additionalMetaTags={[
                {
                  name: 'keywords',
                  content: metaKeywords,
                },
                {
                  name: 'robots',
                  content: metaRobots,
                },
              ]}
            />

       
                       
            <div style={{ position: 'relative', zIndex: 1000, width: '100%' }}>
              <TopicsDropdown />
            </div>
           
      <div style={{ backgroundColor: "#F4F8FA" }}>
            <div className="container">
              <Breadcrumb items={breadcrumbItems} headlinetext={Headline_text}/>
            </div>
        <article className={styles.article}>
        <p className={styles.posteddate}>{post_first_section.posted_date}</p>

          <div className={styles.container}>
            <div className={styles.mainContent}>
              <header className={styles.header}>
                <div className={styles.headerImage} >

                  <Image
                    src={`${apiUrl}${post_first_section.slug_image.data.attributes.url}`}
                    alt={post_first_section.slug_image.data.attributes.alternativeText}
                    width={0}
                    height={0}
                    sizes='100vw'
                    priority
                    className={styles.image}
                  />
                </div>


                <div className={styles.headerContent}>

                  <div className="d-flex justify-content-between align-items-center mt-4">
                  <TopicsComponentForSlug Topics={Topics} Post_type={Post_type}/>
                    <div className="d-flex justify-content-end">
                      {/* <div className={`${styles.categoryIcon} `}>
                  <Image width={20} height={20} src={`${apiUrl}${post_first_section.category_icon.data.attributes.url}`} alt="Category Icon" />
                  </div> */}
                    </div>
                  </div>



                  <h1>{post_first_section.post_name}</h1>
                  <p className={styles.description}>
                    <b>Summary:</b>{' '}
                    {post_first_section.summary?.length > 150 ? (
                      <>
                        {showFullSummary ? post_first_section.summary : truncatedSummary}{' '}

                        <span
                          onClick={toggleSummary}
                          className={`${styles.readMoreBtn} pointer text-decoration-underline`}
                        >
                          {showFullSummary ? 'Read Less' : 'Read More'}
                        </span>
                      </>
                    ) : (
                      post_first_section.summary
                    )}
                  </p>
                </div>
              </header>

              <div className={styles.content} style={{position: 'relative'}}>
                <div className={styles.podcastPlayer}>
                  <div className={styles.playerLayout}>
                    <div className={styles.imageSection}>
                      {audioSection.audio_image?.data && (
                        <Image
                          src={`${apiUrl}${audioSection.audio_image.data.attributes.url}`}
                          alt={post_first_section.post_name}
                          width={180}
                          height={180}
                          className={styles.episodeImage}
                        />
                      )}
                    </div>
                    <div className={styles.contentSection} >
                      {/* <LeadForm /> */}
                      <div className={styles.playerHeader} >
                        <div className={styles.episodeInfo}>
                          <div className='d-flex justify-content-between'>
                            <h2>{audioSection.audio_title}</h2>
                            {audioSection.icon.data &&  <Image
                          src={`${apiUrl}${audioSection.icon.data.attributes.url}`}
                          alt={post_first_section.post_name}
                          width={36}
                          height={36}
                          className={styles.spotifyIcon}
                        />}
                          </div>
                          <p className={styles.audioposteddate}>{audioSection.audio_date}</p>
                        </div>
                      </div>
                      <div className={styles.audioPlayerWrapper}>
                       

<div className={styles.audioPlayerWrapper}>
                        <CustomAudioPlayer 
                          audioUrl={audioSection.audio_url}
                        />
                      </div>

                        
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <aside>
            <div className={styles.asidemain} style={{ top: professor_section && shareArticle.length > 0 ? "0.5rem" : professor_section && shareArticle.length == 0 ? "6.6rem" : "8rem" }}>
                {shareArticle.length > 0 && <ShareArticle styles={styles} shareArticle={shareArticle} apiUrl={apiUrl} title={articlesData.data[0].attributes.Share_article_title} />}
                {professor_section && <FacultySection shareArticle={shareArticle} professor_section={professor_section} apiUrl={apiUrl} />}
                <div >
                  {track_details.length > 0 && <h4 className={styles.programmesforyou}>
                    {articlesData.data[0].attributes.programme_for_you_title}
                  </h4>}

                  <ProgeammesForYou track_details={track_details} apiUrl={apiUrl} currentQuery={currentQuery} />

                </div>
              </div>
            </aside>
          </div>
        </article>
        {/* {combinedArray.length > 0 &&
          <div className={styles.relatedArticles}>
            <h3>Recommended for you</h3>
            <div className="d-flex gap-3 justify-content-md-start justify-content-center">
              {combinedArray.map((article, index) => (
                <div key={index} className={`${styles.relatedCard} col-md-4 col-12 mb-3 px-0 `}
                  onClick={() => router.push(article.attributes.post_id)}
                >
                  {(article.attributes.image?.data?.attributes?.url) ? (
                    <div className={styles.programmeImage}>
                      <Image
                        src={`${apiUrl}${article.attributes.image.data.attributes.url}`}
                        alt={article.attributes.post_name}
                        fill
                        className={styles.image}
                      />
                      <div className={styles.iconWrapper}>
                        <Image
                          src={`${apiUrl}${article.attributes.category_icon.data.attributes.url}`}
                          alt="Bookmark"
                          width={40}
                          height={40}
                        />
                      </div>
                    </div>
                  ) : null}

                  <div className={`${styles.articleContent} d-flex flex-column flex-grow-1`}>
                    <div>
                      <p className={styles.category}>{article.attributes.subtext}</p>
                      <h4 className={styles.title}>{article.attributes.post_name}</h4>
                      <p className={styles.subtext}>{article.attributes.short_summary}</p>
                    </div>
                    <div className={`${styles.metaInfo} mt-auto`}>
                      <span>{article.attributes.author_name}</span>
                      <span>{article.attributes.readTime}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>} */}
      </div>
      <TrackPageBottomFold navData={navbarData} getIsbMail={"isbemail"} apiUrl={apiUrl} />
    </>
  );
}


export const getStaticPaths = async () => {
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/podcasts?populate=deep,10`);
  const response = await res.json();
  const paths = response.data.map((podcast) => {
    const slug = String(podcast.attributes.post_id);
    return {
      params: {
        slug: slug
      }
    };
  });

  return {
    paths,
    fallback: 'blocking'
  };
};

export async function getStaticProps({ params }) {
  const { slug } = params;
  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;

  const [articlesRes, metaRes] = await Promise.all([
    fetch(`${APIUrl}/api/podcasts?filters[post_id][$eq]=${slug}&populate=deep,4`),
    fetch(`${APIUrl}/api/edge-main-page?populate=meta_tags.image`)
  ]);

  const [articlesData, metaTagsData] = await Promise.all([
    articlesRes.json(),
    metaRes.json()
  ]);

  return {
    props: {
      articlesData: articlesData,
perspectivesMetaData: metaTagsData,
      apiUrl: APIUrl,
      baseurl: Baseurl,
    },
    revalidate: 120,
  };
}
 
 