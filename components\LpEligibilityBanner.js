import Image from 'next/image'
import React from 'react'
import { FinanceLinks } from '../pages/lpv2/[learning_track_id]'

const LpEligibilityBanner = ({lpthankyoupageflag, verfiedReport,handleShowPaymentModal,landingPageResponse, classes,RupessDocuments, Group, projectduration}) => {
  const eligibilityContentCss = 'col-3 text-md-end px-0 px-md-2 text-start'
  const marginTB = 'my-lg-3'
  return (
    <div className={`${classes.equalPadding} mx-auto `}>
            <div className="row py-3 px-lg-0 ps-5 pe-4" >
              <div className={`col-lg-3 col  ${classes.rightBorder} ${marginTB}`} >
                <div className={`row ${classes.small_img} ps-md-3 ps-2 ps-lg-0`}>
                  <div className={eligibilityContentCss}>
                    <Image src={Group} alt="Card" width="0" height="0" />
                  </div>
                  <div className={`col-lg-8 col-12 p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`} >
                      {" "}
                      { landingPageResponse.lt_data.start_title}{" "}
                    </p>
                    <div className={``} >
                      <p className={`p-0 m-0 ${classes.new_date_line}`}>
                        {" "}
                        {landingPageResponse.lt_data.start_date}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                      {landingPageResponse.lt_data.last_date_apply_title}{" "}
                        {landingPageResponse.lt_data.end_date}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className={`col-lg-3 col-6  ${classes.rightBorder} ${marginTB}`}>
                <div className={`row ps-md-3 ps-2 ps-lg-0`}>
                  <div className={eligibilityContentCss}>
                    <Image
                      src={projectduration}
                      alt="Card"
                      width="0"
                      height="0"
                      // sizes="100vw"
                    />
                  </div>
                  <div className={`col-lg-8 col-12 p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {landingPageResponse.lt_data.duration_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {landingPageResponse.lt_data.duration_in_weeks} Weeks,
                        Online
                      </p>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {landingPageResponse.lt_data.duration_in_hrs} hours per
                        week
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <hr className='my-3 d-lg-none d-block'/>
              <div className={`col-lg-3 col-6  ${classes.rightBorder} ${marginTB} `} >
                <div className={`row ps-md-3 ps-2 ps-lg-0`}>
                  <div className={eligibilityContentCss}>
                    <Image
                      src={RupessDocuments}
                      alt="Card"
                      width="0"
                      height="0"
                      // sizes="100vw"
                    />
                  </div>
                  <div className={`col-lg-9 col-12 p-0 pe-2`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {landingPageResponse.lt_data.fee_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        INR {landingPageResponse.lt_data.fee}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                      {/* View payment plan */}
                      <FinanceLinks lpthankyoupageflag={lpthankyoupageflag} handleShowPaymentModal={handleShowPaymentModal} landingPageResponse={landingPageResponse}/>
                    </p>

                    {/* <p className={`p-0 m-0 ${classes.last_last_line}`}>
                      {landingPageResponse.lt_data.enterprise_pricing}
                    </p> */}
                    </div>
                  </div>
                </div>
              </div>
              <div className={`col-lg-3 col ${marginTB} ${"classes.rightBorder"}`}>
                <div className={`row pt-2 pt-lg-0 ps-md-3 ps-2 ps-lg-0`}>
                  <div className={eligibilityContentCss}>
                    <Image
                      src={verfiedReport}
                      alt="Card"
                      width="0"
                      height="0"
                    />
                  </div>
                  <div className={`col-lg-8 col-12 p-0 `}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {" "}
                      {landingPageResponse.lt_data.eligibility_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        {/* Any Graduate/ Diploma holder */}
                        {landingPageResponse.lt_data.eligibility_content}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
  )
}

export default LpEligibilityBanner