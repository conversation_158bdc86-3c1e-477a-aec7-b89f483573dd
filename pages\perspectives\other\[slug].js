import { useRouter } from 'next/router';
import styles from './other.module.css';
import Breadcrumb from '../../../components/Breadcrumb/Breadcrumb';

export default function OtherPage() {
  const router = useRouter();
  const { slug } = router.query;

  const breadcrumbItems = [
    { label: 'Perspectives', href: '/perspectives' },
    { label: 'Other', href: '/perspectives/other' },
    { label: slug }
  ];

  return (
    <div className={styles.container}>
      <Breadcrumb items={breadcrumbItems} />
      <div className={styles.header}>
        <h1 className={styles.title}>Content: {slug}</h1>
      </div>
      <div className={styles.meta}>
        <span>By Author Name</span>
        <span>Published: Jan 2024</span>
      </div>
      <div className={styles.content}>
        {/* Add your content here */}
      </div>
      <div className={styles.attachments}>
        <h3>Related Resources</h3>
        <ul className={styles.attachmentList}>
          {/* Add attachments here */}
        </ul>
      </div>
    </div>
  );
}
